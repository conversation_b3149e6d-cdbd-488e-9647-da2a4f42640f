from pydantic import BaseModel, Field
from typing import Optional, List, Literal, Any, Dict


# Structured Output Models for AI Responses in Oracle to PostgreSQL Migration


class CorrectedStatement(BaseModel):
    """
    Pydantic model representing a corrected SQL statement in the migration workflow.

    This model captures the details of AI-generated corrections for problematic PostgreSQL
    statements, including the original statement, corrected version, and detailed change
    documentation for audit trail purposes.

    Attributes:
        statement_number: Sequential number of the statement in the target code
        original_statement: The original PostgreSQL statement before correction
        corrected_statement: The AI-generated corrected PostgreSQL statement
        statement_type: Context type (before_error, error_statement, after_error)
        changes_made: Detailed description of changes made by AI for transparency
    """
    statement_number: int = Field(description="Sequential statement number in target code")
    original_statement: str = Field(description="Original PostgreSQL statement before correction")
    corrected_statement: str = Field(description="AI-generated corrected PostgreSQL statement")
    statement_type: str = Field(description="Statement context type: before_error, error_statement, or after_error")
    changes_made: str = Field(description="Detailed description of AI-generated changes for audit trail")


class StatementConversionOutput(BaseModel):
    """
    Pydantic model for AI-generated statement conversion results.

    Captures the complete output from AI statement conversion operations including
    all corrected statements and a comprehensive explanation of the conversion process.

    Attributes:
        corrected_statements: List of all statements corrected by AI with detailed change tracking
        explanation: Comprehensive summary of errors identified and fixes applied by AI
    """
    corrected_statements: List[CorrectedStatement] = Field(description="Complete list of AI-corrected statements with change details")
    explanation: str = Field(description="Comprehensive summary of errors identified and AI-generated fixes applied")


class Phase1ErrorIdentificationOutput(BaseModel):
    """
    Pydantic model for Phase 1 AI error statement identification results.

    Captures the results of the first phase of AI error analysis where the primary
    error statement is identified from deployment error messages using AI reasoning.

    Attributes:
        error_statement_number: Statement number identified as causing the deployment error
        confidence_score: AI confidence level in the identification (0.0 to 1.0)
        reasoning: Detailed AI reasoning explaining why this statement was identified
    """
    error_statement_number: int = Field(description="Statement number identified by AI as causing deployment error")
    confidence_score: float = Field(description="AI confidence score between 0.0 and 1.0 for identification accuracy")
    reasoning: str = Field(description="Detailed AI reasoning explaining the error statement identification")


class TargetStatementItem(BaseModel):
    """
    Pydantic model for target statement context items in error analysis.

    Represents individual statements in the error context including their position
    and role in the error scenario (before, error, or after the problematic statement).

    Attributes:
        target_statement_number: Sequential number of the statement in target code
        statement_type: Role of statement in error context (before_error, error_statement, after_error)
    """
    target_statement_number: int = Field(description="Sequential statement number in PostgreSQL target code")
    statement_type: str = Field(description="Statement role in error context: before_error, error_statement, or after_error")


class Phase2ErrorContextOutput(BaseModel):
    """
    Pydantic model for Phase 2 AI error context creation results.

    Captures the results of the second phase of AI error analysis where context
    statements around the identified error are selected and validated.

    Attributes:
        target_statements: List of statements forming the error context (before/error/after)
        validation_notes: AI-generated notes about the context creation and validation
    """
    target_statements: List[TargetStatementItem] = Field(description="AI-selected statements forming error context")
    validation_notes: str = Field(description="AI-generated validation notes for error context creation")


class ValidationOutput(BaseModel):
    """
    Pydantic model for AI validation results across the migration workflow.

    Provides standardized validation output format used throughout the workflow
    for consistent AI validation of error identification, mapping, and conversion.

    Attributes:
        is_correct: Boolean indicating whether the validation passed
        explanation: Detailed AI explanation of the validation decision
        confidence: AI confidence level in the validation result (0.0 to 1.0)
    """
    is_correct: bool = Field(description="Boolean indicating whether AI validation passed")
    explanation: str = Field(description="Detailed AI explanation of validation decision and reasoning")
    confidence: float = Field(description="AI confidence score between 0.0 and 1.0 for validation accuracy")


class SyntaxValidationOutput(BaseModel):
    """Model for syntax validation AI output."""
    is_syntactically_equivalent: bool = Field(description="Whether statements are syntactically equivalent")
    explanation: str = Field(description="Explanation of the validation result")
    equivalence_score: float = Field(description="Equivalence score between 0.0 and 1.0")
    translation_quality: str = Field(description="Quality of translation: excellent, good, fair, poor")
    syntax_differences: List[str] = Field(description="List of identified syntax differences")


class StatementMappingItem(BaseModel):
    """Model for a single statement mapping."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")
    reason: str = Field(description="Reason for this mapping")


class MappingOutput(BaseModel):
    """Model for mapping AI output."""
    mappings: List[StatementMappingItem] = Field(description="List of statement mappings")


class WrongMappingItem(BaseModel):
    """Model for a wrong mapping identification."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    reason: str = Field(description="Reason why this mapping is wrong")


class WrongMappingOutput(BaseModel):
    """Model for wrong mapping identification AI output."""
    wrong_mappings: List[WrongMappingItem] = Field(description="List of wrong mappings")


class Phase1IdentificationOutput(BaseModel):
    """Model for Phase 1 error statement identification."""
    source_statement_number: int = Field(description="The identified source statement number")
    confidence_score: float = Field(description="Confidence score between 0.0 and 1.0")
    reasoning: str = Field(description="Reasoning for the identification")


class SourceStatementItem(BaseModel):
    """Model for a source statement mapping item."""
    target_statement_number: int = Field(description="Target statement number")
    source_statement_number: int = Field(description="Source statement number")
    statement_type: str = Field(description="Type: before_error, error_statement, or after_error")


class Phase2MappingOutput(BaseModel):
    """Model for Phase 2 sequential mapping output."""
    source_statements: List[SourceStatementItem] = Field(description="List of source statement mappings")
    validation_notes: str = Field(description="Validation notes for the mapping")


class StatementMapping(BaseModel):
    """Mapping between source and target statements."""
    source_statement: str = Field(description="The source statement")
    source_line_number: int = Field(description="The line number of the source statement (1-based)")
    target_statement: str = Field(description="The target statement")
    target_line_number: int = Field(description="The line number of the target statement (1-based)")
    status: Literal["Matched", "Source Only", "Target Only", "Target Only (Duplicate)"] = Field(
        description="The status of the mapping: Matched, Source Only, Target Only, or Target Only (Duplicate)"
    )


class ErrorContext(BaseModel):
    """Context around an error statement."""
    before_statement: str = Field(description="Statement before the error statement")
    before_statement_number: int = Field(description="Line number of the statement before the error")
    error_statement: str = Field(description="The statement with the error")
    error_statement_number: int = Field(description="Line number of the error statement")
    after_statement: str = Field(description="Statement after the error statement")
    after_statement_number: int = Field(description="Line number of the statement after the error")


class WorkflowState(BaseModel):
    """Workflow state for code processing pipeline."""
    source_code: str = Field(
        description="Original Source code to be processed"
    )
    target_code: str = Field(
        description="Original Target code to be processed"
    )
    deployment_error: Optional[str] = Field(
        default=None,
        description="Error message from deployment"
    )
    source_statements: Optional[List[str]] = Field(
        default=None,
        description="Source SQL statements after splitting"
    )
    target_statements: Optional[List[str]] = Field(
        default=None,
        description="Target SQL statements after splitting"
    )
    target_error_context: Optional[ErrorContext] = Field(
        default=None,
        description="Context around the error statement"
    )
    source_context: Optional[ErrorContext] = Field(
        default=None,
        description="Corresponding source context for the error"
    )
    corrected_target_statements: Optional[List[str]] = Field(
        default=None,
        description="Target statements after correction"
    )
    updated_target_code: Optional[str] = Field(
        default=None,
        description="Updated target code after correction"
    )
    deployment_successful: Optional[bool] = Field(
        default=None,
        description="Whether the deployment was successful"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message from deployment if it failed"
    )
    validation_successful: Optional[bool] = Field(
        default=None,
        description="Whether the error identification validation was successful"
    )
    validation_attempts: Optional[int] = Field(
        default=0,
        description="Number of validation attempts performed"
    )
    source_mapping_successful: Optional[bool] = Field(
        default=None,
        description="Whether the source mapping validation was successful"
    )
    source_mapping_attempts: Optional[int] = Field(
        default=0,
        description="Number of source mapping validation attempts performed"
    )
    conversion_successful: Optional[bool] = Field(
        default=None,
        description="Whether the conversion validation was successful"
    )
    conversion_attempts: Optional[int] = Field(
        default=0,
        description="Number of conversion validation attempts performed"
    )

    iteration_count: Optional[int] = Field(
        default=1,
        description="Current iteration count for the workflow"
    )

    mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Mappings between source and target statements"
    )

    # Validation feedback fields for AI learning
    error_identification_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from error identification validation failure"
    )
    source_mapping_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from source mapping validation failure"
    )
    conversion_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from conversion validation failure"
    )

    # AI corrections fields for new workflow
    ai_corrections: Optional[List[CorrectedStatement]] = Field(
        default=None,
        description="AI-generated corrections from Convert_TargetStatement node"
    )
    conversion_explanation: Optional[str] = Field(
        default=None,
        description="Explanation from AI conversion process"
    )
    original_target_statements: Optional[List[str]] = Field(
        default=None,
        description="Original target statements before applying corrections"
    )

    # Position mapping fields for enhanced error resolution (serializable dictionaries)

    target_position_mapper: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Serialized position mapper for target statements"
    )

